# 搜索标签功能实现文档

## 概述

本文档描述了**可移除标签的搜索条件（Removable Filter Tags / Search Chips）**功能的完整实现。这个功能允许用户可视化地查看当前应用的所有搜索条件，并通过点击标签上的删除按钮来移除特定的搜索条件。

## 功能特性

### 1. 核心功能
- **可视化展示**：将所有搜索条件以标签形式展示
- **一键移除**：点击标签上的 × 按钮移除单个条件
- **批量清除**：提供"Clear All"按钮清除所有条件
- **智能分类**：不同类型的搜索条件使用不同的图标和颜色

### 2. 支持的搜索条件类型
- **综合搜索** (`query`): 全文搜索关键词
- **简单筛选** (`filter`): 左侧面板的筛选条件
- **高级搜索** (`advanced`): 复杂的多条件组合搜索
- **排序条件** (`sort`): 当前的排序设置

### 3. 用户体验优化
- **紧凑模式**：适用于空间有限的场景
- **最大显示数量**：避免标签过多影响界面
- **响应式设计**：适配不同屏幕尺寸
- **即时反馈**：移除标签后立即更新搜索结果

## 技术架构

### 1. 核心组件

#### SearchChips 组件
```typescript
interface SearchChip {
  id: string;
  type: 'query' | 'filter' | 'advanced' | 'sort';
  label: string;
  value: string | number | boolean;
  displayValue: string;
  field?: string;
  operator?: string;
  removable?: boolean;
  color?: 'default' | 'secondary' | 'destructive' | 'outline';
}
```

#### useSearchChips Hook
提供统一的搜索条件管理逻辑：
- 自动转换不同类型的搜索条件为标签
- 处理标签移除操作
- 管理搜索状态更新

### 2. 工具函数

#### searchChipsUtils.ts
- `filtersToChips()`: 将简单过滤器转换为标签
- `advancedConditionsToChips()`: 将高级搜索条件转换为标签
- `sortToChip()`: 将排序条件转换为标签
- `combineSearchChips()`: 组合所有搜索条件
- `parseChipRemoval()`: 解析标签移除操作

## 使用方法

### 1. 基础用法

```tsx
import SearchChips from '@/components/SearchChips';
import { useSearchChips } from '@/hooks/useSearchChips';

function MyComponent() {
  const searchChips = useSearchChips({
    filters: appliedFilters,
    advancedConditions: advancedSearchConditions,
    sortBy,
    sortOrder,
    fieldConfigs: config?.fields,
    onFilterChange: handleFilterChange,
    onAdvancedConditionRemove: handleAdvancedConditionRemove,
    onSortChange: handleSortChange,
    onClearAll: handleClearAll
  });

  return (
    <SearchChips
      chips={searchChips.chips}
      onRemoveChip={searchChips.handleRemoveChip}
      onClearAll={searchChips.handleClearAll}
      showClearAll={true}
      maxDisplay={8}
      compact={false}
    />
  );
}
```

### 2. 简化用法（仅处理简单过滤器）

```tsx
import { useSimpleSearchChips } from '@/hooks/useSearchChips';

function SimpleSearchComponent() {
  const searchChips = useSimpleSearchChips(
    filters,
    handleFilterChange,
    fieldConfigs
  );

  return (
    <SearchChips
      chips={searchChips.chips}
      onRemoveChip={searchChips.handleRemoveChip}
      onClearAll={searchChips.handleClearAll}
      compact={true}
    />
  );
}
```

### 3. URL参数管理

```tsx
import { useUrlSearchChips } from '@/hooks/useSearchChips';

function UrlSearchComponent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const handleParamChange = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams);
    if (value === null) {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    router.push(`?${params.toString()}`);
  };

  const searchChips = useUrlSearchChips(
    searchParams,
    handleParamChange,
    fieldConfigs
  );

  return <SearchChips {...searchChips} />;
}
```

## 配置选项

### SearchChips 组件属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `chips` | `SearchChip[]` | - | 要显示的搜索标签数组 |
| `onRemoveChip` | `(chipId: string) => void` | - | 移除单个标签的回调 |
| `onClearAll` | `() => void` | - | 清除所有标签的回调 |
| `showClearAll` | `boolean` | `true` | 是否显示清除所有按钮 |
| `maxDisplay` | `number` | - | 最大显示标签数量 |
| `compact` | `boolean` | `false` | 是否使用紧凑模式 |
| `className` | `string` | - | 自定义CSS类名 |

### 字段配置

```typescript
interface FieldConfig {
  fieldName: string;
  displayName: string;
}
```

## 集成示例

### 1. 数据库页面集成

已集成到 `DatabasePageContent.tsx` 中：
- 显示在数据库标题下方
- 包含所有类型的搜索条件
- 移除标签后自动重新加载数据

### 2. 全局搜索集成

`GlobalSearchWithChips.tsx` 组件：
- 管理搜索历史
- 支持快速重新搜索
- 智能去重和数量限制

## 性能考虑

### 1. 避免过度渲染
- 使用 `useMemo` 缓存标签数组
- 使用 `useCallback` 缓存事件处理函数
- 合理设置依赖数组

### 2. 内存管理
- 限制搜索历史数量
- 及时清理无用的搜索条件
- 避免创建过多的标签对象

### 3. 用户体验
- 设置合理的最大显示数量
- 提供紧凑模式选项
- 使用防抖处理频繁操作

## 扩展性

### 1. 自定义标签类型
可以通过扩展 `SearchChip['type']` 来支持新的搜索条件类型：

```typescript
type CustomChipType = 'query' | 'filter' | 'advanced' | 'sort' | 'custom';
```

### 2. 自定义样式
通过修改 `getChipVariant` 函数来自定义不同类型标签的样式。

### 3. 国际化支持
通过配置文件管理字段显示名称和操作符显示名称。

## 测试页面

- `/test-search-chips`: 基础功能测试页面
- `/demo-search-chips`: 完整功能演示页面

这些页面展示了搜索标签功能的各种使用场景和配置选项。
